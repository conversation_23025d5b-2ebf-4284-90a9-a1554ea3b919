<?php

namespace App\Modules\RequestDelete\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\RequestDelete\Services\DomainEppCancellationJobService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\DomainParser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Exception;
use Throwable;

class DomainEppCancellation implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $domainId,
        string $domainName,
        string $userId,
        string $userEmail,
        string $reason,
        string $createdDate,
        string $supportNote = null,
        int $adminId = null,
        string $adminName = null,
        string $adminEmail = null
    ) {
        $registry = DomainParser::getRegistryName($domainName);

        $this->params = [
            'domainId' => $domainId,
            'domainName' => $domainName,
            'userId' => $userId,
            'userEmail' => $userEmail,
            'reason' => $reason,
            'createdDate' => $createdDate,
            'supportNote' => $supportNote,
            'adminId' => $adminId,
            'adminName' => $adminName,
            'adminEmail' => $adminEmail
        ];

        $this->onConnection(QueueConnection::DOMAIN_CANCELLATION);
        $this->onQueue(QueueTypes::DOMAIN_CANCELLATION[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): string
    {
        return $this->params['domainId'] . '_' . $this->params['domainName'];
    }

    public function handle(): void
    {
        try {
            DomainEppCancellationJobService::instance()->eppDelete($this->params);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
            app(AuthLogger::class)->info('number of attempts: ' . $this->attempts());
            throw $e;
        }
    }

    public function backoff(): array
    {
        return [5, 10];
    }

    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error($exception->getMessage());
    }
}
