<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Notification\Services\NotificationService;
use App\Util\Constant\UserDomainStatus;
use App\Util\Constant\DomainStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use App\Modules\CustomLogger\Services\AuthLogger;

class DomainEppCancellationJobService
{
    public static function instance()
    {
        return new self;
    }

    public function eppDelete(array $domain): void
    {
        $eppInfoResponse = EppDomainService::instance()->callEppDomainInfo($domain['domainName']);
        $datastoreInfoResponse = EppDomainService::instance()->callDatastoreDomainInfo($domain['domainName']);

        if (!isset($eppInfoResponse['data']) || !isset($datastoreInfoResponse['data'])) {
            app(AuthLogger::class)->error("Domain {$domain['domainName']} not found in EPP or datastore");
            NotificationService::sendFailedDeleteNotif($domain['domainName']);
            throw new \Exception("Domain {$domain['domainName']} not found in EPP or datastore");
        }

        $eppInfo = $eppInfoResponse['data'];
        $datastoreInfo = $datastoreInfoResponse['data'];

        if (in_array('clientDeleteProhibited', $eppInfo['status'])) {
            app(AuthLogger::class)->info("Domain {$domain['domainName']} has clientDeleteProhibited status, cannot delete");
            NotificationService::sendFailedDeleteNotif($domain['domainName']);
            throw new \Exception("Domain {$domain['domainName']} has clientDeleteProhibited status");
        }

        EppDomainService::instance()->callEppDomainDelete($domain['domainName']);
        EppDomainService::instance()->callDatastoreDomainDelete($domain['domainName']);

        $this->updateLocalDatabase($domain);
    }

    private function updateLocalDatabase(array $domain): void
    {
        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $domain['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $domain['domainId'])
            ->update($updates);

        $this->updateDomainCancellationRequest($domain);

        DB::client()->table('pending_domain_deletions')->insert([
            'registered_domain_id' => $domain['domainId'],
            'deleted_by' => $domain['userEmail'],
            'deleted_at' => $timestamp,
            'created_at' => $timestamp,
            'updated_at' => $timestamp,
        ]);
    }

    private function updateDomainCancellationRequest(array $domain): void
    {
        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $domain['domainId'])
            ->exists();

        if (!$exists) {
            $this->createDomainCancellationRequest($domain);
            return;
        }

        $date = Carbon::parse($domain['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        $adminId = $domain['adminId'] ?? 1;
        $adminName = $domain['adminName'] ?? 'System';
        $adminEmail = $domain['adminEmail'] ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";
        $supportNote = $domain['supportNote'] ?? "{$adminFullName} deleted domain: {$domain['domainName']}";

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $domain['domainId'])
            ->update([
                'support_agent_id'   => $adminId,
                'support_agent_name' => $adminFullName,
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => $supportNote,
                'is_refunded'        => $is_refunded,
            ]);
    }

    private function createDomainCancellationRequest(array $domain): void
    {
        $date = Carbon::parse($domain['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        $adminId = $domain['adminId'] ?? 1;
        $adminName = $domain['adminName'] ?? 'System';
        $adminEmail = $domain['adminEmail'] ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";
        $supportNote = $domain['supportNote'] ?? "{$adminFullName} deleted domain: {$domain['domainName']}";

        DB::client()->table('domain_cancellation_requests')->insert([
            'user_id'             => $domain['userId'],
            'domain_id'           => $domain['domainId'],
            'reason'              => $domain['reason'],
            'support_agent_id'    => $adminId,
            'support_agent_name'  => $adminFullName,
            'deleted_at'          => now(),
            'feedback_date'       => now(),
            'support_note'        => $supportNote,
            'is_refunded'         => $is_refunded,
            'requested_at'        => now(),
        ]);
    }
}
