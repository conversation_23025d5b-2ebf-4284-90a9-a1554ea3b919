<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use Illuminate\Support\Facades\Auth;

class DomainDeleteApprovalService
{
    public function approve($request): void
    {
        $data = $request->all();
        $admin = $this->getAdminInfo();
        
        DomainEppCancellation::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userID'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request',
            $data['createdDate'] ?? now()->toDateTimeString(),
            "Request delete approved by {$admin['email']}",
            $admin['id'],
            $admin['name'],
            $admin['email']
        );

        $this->sendNotifications($data);
    }

    private function getAdminInfo(): array
    {
        return [
            'id' => Auth::id() ?? 1,
            'name' => Auth::user()->name ?? 'System',
            'email' => Auth::user()->email ?? '<EMAIL>'
        ];
    }

    private function sendNotifications(array $data): void
    {
        DomainDeleteService::userNotification($data);
        DomainDeleteService::userEmailNotification($data);
    }
}
